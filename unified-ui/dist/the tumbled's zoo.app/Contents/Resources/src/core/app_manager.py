"""
Application Manager for Unified UI

Handles launching and managing registered applications.
"""

import os
import subprocess
import sys
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Callable
from .config_manager import ConfigManager


class AppManager:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.running_processes = {}  # app_name -> process info
        self.log_callbacks = []  # List of callback functions for log updates
        self.logs_dir = os.path.expanduser("~/Library/Logs/tumbleds-zoo-apps")
        os.makedirs(self.logs_dir, exist_ok=True)

    def add_log_callback(self, callback: Callable[[str, str], None]):
        """Add a callback function to receive log updates."""
        self.log_callbacks.append(callback)

    def remove_log_callback(self, callback: Callable[[str, str], None]):
        """Remove a log callback function."""
        if callback in self.log_callbacks:
            self.log_callbacks.remove(callback)

    def _notify_log_update(self, app_name: str, log_content: str):
        """Notify all callbacks about log updates."""
        # Create a copy of callbacks to avoid modification during iteration
        callbacks_copy = self.log_callbacks.copy()
        for callback in callbacks_copy:
            try:
                # Check if callback is still valid before calling
                if callback in self.log_callbacks:
                    callback(app_name, log_content)
            except Exception as e:
                print(f"Error in log callback: {e}")
                # Remove problematic callback to prevent future errors
                if callback in self.log_callbacks:
                    self.log_callbacks.remove(callback)

    def get_app_log_path(self, app_name: str) -> str:
        """Get the log file path for an application."""
        safe_name = "".join(c for c in app_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_')
        return os.path.join(self.logs_dir, f"{safe_name}.log")

    def get_app_logs(self, app_name: str, max_lines: int = 100) -> str:
        """Get recent logs for an application."""
        log_path = self.get_app_log_path(app_name)
        if not os.path.exists(log_path):
            return ""

        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # Return last max_lines
                return ''.join(lines[-max_lines:])
        except Exception as e:
            return f"Error reading log file: {e}"

    def clear_app_logs(self, app_name: str):
        """Clear logs for an application."""
        log_path = self.get_app_log_path(app_name)
        try:
            if os.path.exists(log_path):
                open(log_path, 'w').close()
        except Exception as e:
            print(f"Error clearing logs for {app_name}: {e}")

    def get_registered_apps(self) -> List[Dict]:
        """Get list of all registered applications."""
        return self.config_manager.get_apps()

    def register_app(self, app_info: Dict):
        """Register a new application."""
        # Validate required fields
        required_fields = ['name', 'command', 'description']
        for field in required_fields:
            if field not in app_info:
                raise ValueError(f"Missing required field: {field}")

        # Set default values for optional fields
        if 'logo' not in app_info:
            app_info['logo'] = ''
        if 'working_directory' not in app_info:
            app_info['working_directory'] = ''

        self.config_manager.add_app(app_info)

    def register_from_file(self, register_file_path: str):
        """Register an application from a register_app.json file."""
        app_info = self.config_manager.load_register_file(register_file_path)
        if app_info:
            # Convert relative paths to absolute paths based on the register file location
            register_dir = os.path.dirname(os.path.abspath(register_file_path))

            # Convert relative command path to absolute
            if 'command' in app_info and not os.path.isabs(app_info['command']):
                app_info['command'] = os.path.join(register_dir, app_info['command'])

            # Convert relative logo path to absolute
            if 'logo' in app_info and app_info['logo'] and not os.path.isabs(app_info['logo']):
                app_info['logo'] = os.path.join(register_dir, app_info['logo'])

            # Set working directory to the register file directory if not specified
            if 'working_directory' not in app_info or not app_info['working_directory']:
                app_info['working_directory'] = register_dir

            self.register_app(app_info)
            return True
        return False

    def unregister_app(self, app_name: str):
        """Unregister an application."""
        self.config_manager.remove_app(app_name)

    def launch_app(self, app_name: str) -> bool:
        """Launch an application by name."""
        apps = self.get_registered_apps()
        app_info = None

        for app in apps:
            if app['name'] == app_name:
                app_info = app
                break

        if not app_info:
            print(f"Application '{app_name}' not found")
            return False

        return self._execute_command(app_info)

    def _execute_command(self, app_info: Dict) -> bool:
        """Execute the application command with log collection."""
        try:
            command = app_info['command']
            working_dir = app_info.get('working_directory', '')
            app_name = app_info['name']

            # Determine the working directory
            if working_dir and os.path.exists(working_dir):
                cwd = working_dir
            else:
                cwd = os.getcwd()

            # Prepare log file
            log_path = self.get_app_log_path(app_name)

            # Write launch info to log
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            launch_info = f"\n=== {timestamp} - Launching {app_name} ===\n"
            launch_info += f"Command: {command}\n"
            launch_info += f"Working Directory: {cwd}\n"
            launch_info += "=" * 50 + "\n"

            with open(log_path, 'a', encoding='utf-8') as log_file:
                log_file.write(launch_info)

            # Determine command to execute
            cmd_args = None
            if os.path.exists(command) and os.path.isfile(command):
                # Get the file extension
                _, ext = os.path.splitext(command)
                if ext == '.py':
                    cmd_args = [sys.executable, command]
                elif ext == '.sh':
                    cmd_args = ['bash', command]
                else:
                    cmd_args = [command]
            else:
                # Execute the command directly
                cmd_args = command.split()

            if cmd_args:
                # Start process with log capture
                process = subprocess.Popen(
                    cmd_args,
                    cwd=cwd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1
                )

                # Store process info
                self.running_processes[app_name] = {
                    'process': process,
                    'log_path': log_path,
                    'start_time': datetime.now()
                }

                # Start log monitoring thread
                log_thread = threading.Thread(
                    target=self._monitor_process_output,
                    args=(app_name, process, log_path),
                    daemon=True
                )
                log_thread.start()

                return True
            else:
                return False

        except Exception as e:
            error_msg = f"Error launching application '{app_info['name']}': {e}"
            print(error_msg)
            # Log the error
            try:
                log_path = self.get_app_log_path(app_info['name'])
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                with open(log_path, 'a', encoding='utf-8') as log_file:
                    log_file.write(f"{timestamp} - ERROR: {error_msg}\n")
            except:
                pass
            return False

    def _monitor_process_output(self, app_name: str, process: subprocess.Popen, log_path: str):
        """Monitor process output and write to log file."""
        try:
            with open(log_path, 'a', encoding='utf-8') as log_file:
                while True:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        log_line = f"[{timestamp}] {output}"
                        log_file.write(log_line)
                        log_file.flush()

                        # Notify callbacks about new log content
                        self._notify_log_update(app_name, log_line.strip())

                # Process finished
                return_code = process.poll()
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                finish_msg = f"\n{timestamp} - Process finished with return code: {return_code}\n"
                log_file.write(finish_msg)
                log_file.flush()

                # Remove from running processes
                if app_name in self.running_processes:
                    del self.running_processes[app_name]

                # Notify callbacks
                self._notify_log_update(app_name, finish_msg.strip())

        except Exception as e:
            print(f"Error monitoring process output for {app_name}: {e}")

    def get_running_processes(self) -> Dict[str, Dict]:
        """Get information about currently running processes."""
        return self.running_processes.copy()

    def stop_app(self, app_name: str) -> bool:
        """Stop a running application."""
        if app_name in self.running_processes:
            try:
                process_info = self.running_processes[app_name]
                process = process_info['process']
                process.terminate()

                # Wait a bit for graceful termination
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't terminate gracefully
                    process.kill()
                    process.wait()

                # Log termination
                log_path = process_info['log_path']
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                with open(log_path, 'a', encoding='utf-8') as log_file:
                    log_file.write(f"\n{timestamp} - Process terminated by user\n")

                # Remove from running processes
                del self.running_processes[app_name]
                return True
            except Exception as e:
                print(f"Error stopping application {app_name}: {e}")
                return False
        return False

    def validate_app_info(self, app_info: Dict) -> List[str]:
        """Validate application information and return list of errors."""
        errors = []

        # Check required fields
        required_fields = ['name', 'command', 'description']
        for field in required_fields:
            if field not in app_info or not app_info[field].strip():
                errors.append(f"Missing or empty required field: {field}")

        # Check if command file exists
        if 'command' in app_info and app_info['command']:
            command = app_info['command']
            if not command.startswith('/') and not os.path.exists(command):
                errors.append(f"Command file not found: {command}")

        # Check if logo file exists and is valid (but don't fail validation for logo issues)
        if 'logo' in app_info and app_info['logo']:
            logo_path = app_info['logo']
            if isinstance(logo_path, str):
                logo_path = logo_path.strip()
                if logo_path:  # Only validate non-empty paths
                    # Note: We don't add logo validation errors to the errors list
                    # because logo issues should not prevent app registration
                    # The UI will handle logo display gracefully with fallbacks
                    try:
                        if not os.path.exists(logo_path):
                            print(f"Warning: Logo file not found: {logo_path}")
                        elif not os.path.isfile(logo_path):
                            print(f"Warning: Logo path is not a file: {logo_path}")
                        else:
                            # Check file size
                            try:
                                file_size = os.path.getsize(logo_path)
                                if file_size == 0:
                                    print(f"Warning: Logo file is empty: {logo_path}")
                                elif file_size > 50 * 1024 * 1024:  # 50MB limit
                                    print(f"Warning: Logo file too large: {logo_path}")
                                else:
                                    # Check file extension
                                    valid_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg'}
                                    file_ext = os.path.splitext(logo_path)[1].lower()
                                    if file_ext not in valid_extensions:
                                        print(f"Warning: Unsupported image format: {logo_path}")
                                    else:
                                        # Only test image loading if all other checks pass
                                        try:
                                            from PyQt5.QtGui import QPixmap
                                            test_pixmap = QPixmap()
                                            load_success = test_pixmap.load(logo_path)
                                            if not load_success or test_pixmap.isNull():
                                                print(f"Warning: Logo file is not a valid image: {logo_path}")
                                        except Exception as e:
                                            print(f"Warning: Error validating logo file: {str(e)}")
                            except (OSError, IOError) as e:
                                print(f"Warning: Cannot access logo file: {logo_path} - {str(e)}")
                    except Exception as e:
                        print(f"Warning: Logo validation error: {str(e)}")

        return errors
