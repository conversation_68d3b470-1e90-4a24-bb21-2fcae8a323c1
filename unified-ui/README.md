# the tumbled's zoo

一个动物主题的程序启动器，采用暖米色莫兰迪配色方案，为所有子项目提供统一的程序入口。

## 功能特性

- 🦁 动物主题的程序启动界面
- 🎨 暖米色莫兰迪风格的UI设计
- 📱 紧凑的垂直列表展示，正方形程序图标
- 📜 支持垂直滚动浏览
- ⚙️ 支持手动注册和配置文件注册
- 🗑️ 支持右键取消注册程序
- 📦 支持打包为macOS应用
- 🔧 自动管理程序配置
- 🖼️ LOGO作为程序图标显示在系统中，界面保持简洁
- 📊 **实时日志收集**：自动收集启动程序的标准输出和错误输出
- 📝 **日志管理**：为每个程序单独保存日志文件，支持查看和清空
- 🟢 **运行状态显示**：实时显示程序运行状态
- 🔍 **日志查看器**：内置日志查看器，支持实时更新和自动滚动

## 安装和运行

### 依赖要求

- Python 3.7+
- PyQt5

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行程序

```
python main.py
```

or 

```
./start.sh
```

## 注册新程序

### 方法一：手动注册

1. 点击界面右上角的"注册新程序"按钮
2. 填写程序信息：
   - 程序名称
   - 启动命令或脚本路径，支持python脚本和shell脚本以及直接执行的命令
   - LOGO图片路径（可选）
   - 工作目录（可选）
   - 程序描述
3. 点击"注册"按钮

### 方法二：配置文件注册

1. 在子项目目录下创建 `register_app.json` 文件
2. 在注册界面选择该配置文件，内容会自动加载到手动注册输入框中
3. 可以在输入框中修改后再注册，或直接注册

#### register_app.json 文件格式

```json
{
  "name": "程序名称",
  "command": "启动命令或脚本路径",
  "logo": "LOGO图片路径",
  "description": "程序描述",
  "working_directory": "工作目录"
}
```

#### 字段说明

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `name` | string | ✅ | 程序显示名称，会在启动器界面中显示 |
| `command` | string | ✅ | 程序启动命令或脚本路径，支持相对路径和绝对路径 |
| `description` | string | ✅ | 程序功能描述，用于说明程序的主要功能 |
| `logo` | string | ❌ | 程序图标路径，支持 png、jpg、jpeg、gif、bmp 格式，支持相对路径 |
| `working_directory` | string | ❌ | 程序工作目录，如果不指定则使用配置文件所在目录 |

#### 路径处理规则

- **相对路径**：相对于 `register_app.json` 文件所在目录
- **绝对路径**：直接使用指定的完整路径
- **工作目录**：如果未指定，自动设置为配置文件所在目录

#### 示例配置文件

```json
{
  "name": "图片对比工具",
  "command": "main.py",
  "logo": "config/logo.png",
  "description": "图片对比和差异分析工具，支持多种图片格式。",
  "working_directory": ""
}
```

#### 错误处理

- 如果配置文件格式错误（非有效JSON），会显示格式错误提示
- 如果缺少必需字段（name、command、description），会在注册时进行验证
- 如果缺少可选字段，对应输入框会保持为空，可以手动填写

## 已支持的子项目

- **日志扫描器** (log-scanner): 强大的日志文件扫描和分析工具
- **Git提交工具** (commit-in-one): Git仓库管理和提交工具
- **清理无用照片** (clean-unused-photo): 清理和管理无用的照片文件
- **图片对比工具** (image-compare): 图片对比和差异分析工具
- **Markdown图片验证器** (markdown-image-validator): 验证Markdown文档中的图片链接
- **视频下载工具** (video-download-ui): 视频下载和管理工具

## 打包为macOS应用

### 构建应用包

```bash
python3 simple_build.py
```

### 安装应用

```bash
cp -r "dist/the tumbled's zoo.app" /Applications/
```

### 创建DMG安装包

```bash
hdiutil create -volname "the tumbled's zoo" -srcfolder "dist/the tumbled's zoo.app" -ov -format UDZO "the-tumbleds-zoo.dmg"
```

## 日志功能

### 日志存储位置

程序日志自动保存在：`~/Library/Logs/tumbleds-zoo-apps/`

### 日志功能特性

- **自动收集**：启动程序时自动收集标准输出和错误输出
- **实时显示**：在界面下方的日志查看器中实时显示
- **状态指示**：● 表示程序正在运行，○ 表示程序已停止
- **日志管理**：支持查看历史日志和清空日志
- **时间戳**：每条日志都带有时间戳，便于调试

## 项目结构

```
unified-ui/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖文件
├── simple_build.py         # 简化的macOS应用构建脚本
├── README.md              # 说明文档
├── config/
│   ├── logo.png           # 程序图标
│   └── apps.json          # 应用配置文件
└── src/
    ├── core/
    │   ├── app_manager.py     # 应用管理器（含日志收集功能）
    │   └── config_manager.py  # 配置管理器
    └── ui/
        ├── main_window.py     # 主界面（含日志查看器）
        ├── register_dialog.py # 注册对话框
        └── edit_dialog.py     # 编辑对话框
```

## 配色方案

采用暖米色莫兰迪配色方案：
- 主背景色: #F5F2EE
- 卡片背景: #FEFCFA
- 边框颜色: #E8DDD4
- 文字颜色: #6B5B47
- 强调色: #A0937D

## 许可证

MIT License
