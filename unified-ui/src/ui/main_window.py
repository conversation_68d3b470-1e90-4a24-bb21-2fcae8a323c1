"""
Main Window for Unified UI

Main interface showing registered applications with Morandi color scheme.
"""

import os
import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QGridLayout, QPushButton, QLabel,
                             QScrollArea, QFrame, QMessageBox, QMenuBar, QAction, QDialog, QMenu,
                             QTextEdit, QComboBox, QSplitter, QGroupBox)
from PyQt5.QtCore import Qt, QSize, QTimer
from PyQt5.QtGui import QPixmap, QIcon, QFont

from ..core.app_manager import AppManager
from .register_dialog import RegisterDialog


class CustomConfirmDialog(QDialog):
    """Custom confirmation dialog with Morandi color scheme."""

    def __init__(self, title, message, parent=None):
        super().__init__(parent)
        self.result = False
        self.setup_ui(title, message)
        self.apply_morandi_style()

    def setup_ui(self, title, message):
        self.setWindowTitle(title)
        self.setFixedSize(400, 180)
        self.setModal(True)

        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(30, 25, 30, 25)

        # Message label
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setStyleSheet("font-size: 14px; color: #6B5B47; line-height: 1.4;")
        layout.addWidget(message_label)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        self.no_btn = QPushButton("No")
        self.no_btn.clicked.connect(self.reject)
        self.no_btn.setFixedSize(80, 35)

        self.yes_btn = QPushButton("Yes")
        self.yes_btn.clicked.connect(self.accept)
        self.yes_btn.setFixedSize(80, 35)

        button_layout.addStretch()
        button_layout.addWidget(self.no_btn)
        button_layout.addWidget(self.yes_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def apply_morandi_style(self):
        """Apply Morandi color scheme without question mark icon."""
        style = """
        QDialog {
            background-color: #F5F2EE;
            border-radius: 8px;
        }
        QPushButton {
            background-color: #F0E6D6;
            border: 1px solid #D4C4B0;
            border-radius: 6px;
            color: #6B5B47;
            font-weight: 500;
            font-size: 13px;
        }
        QPushButton:hover {
            background-color: #E8DDD4;
        }
        QPushButton:pressed {
            background-color: #DDD2C6;
        }
        """
        self.setStyleSheet(style)


class AppCard(QFrame):
    def __init__(self, app_info, parent=None):
        super().__init__(parent)
        self.app_info = app_info
        self.parent_window = parent
        self.setup_ui()
        self.apply_warm_beige_style()

    def setup_ui(self):
        self.setFixedHeight(80)
        self.setCursor(Qt.PointingHandCursor)

        layout = QHBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)

        # Logo
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setFixedSize(60, 60)

        # Always start with default style and NEVER change it unless we successfully load an image
        default_style = "border: 1px solid #E8DDD4; border-radius: 8px; background-color: #FEFCFA; font-size: 28px;"
        logo_label.setStyleSheet(default_style)
        logo_label.setText("🐾")

        # Only try to load custom logo if we have a valid, non-empty path
        try:
            logo_path = self.app_info.get('logo')

            # Extensive validation before attempting any image operations
            if (logo_path is not None and
                isinstance(logo_path, str) and
                len(logo_path.strip()) > 0):

                logo_path = logo_path.strip()

                # Check if file exists and is actually a file (not directory)
                if os.path.exists(logo_path) and os.path.isfile(logo_path):

                    # Check file size
                    try:
                        file_size = os.path.getsize(logo_path)
                        if file_size == 0:
                            print(f"Logo file is empty: {logo_path}")
                            # Ensure default state and continue
                            logo_label.setStyleSheet(default_style)
                            logo_label.setText("🐾")
                        elif file_size > 50 * 1024 * 1024:  # 50MB limit
                            print(f"Logo file too large: {logo_path} ({file_size} bytes)")
                            # Ensure default state and continue
                            logo_label.setStyleSheet(default_style)
                            logo_label.setText("🐾")
                        else:
                            # File size is acceptable, proceed with loading
                            self._load_logo_image(logo_path, logo_label, default_style)
                    except (OSError, IOError) as e:
                        print(f"Cannot get file size for: {logo_path} - {e}")
                        # Ensure default state and continue
                        logo_label.setStyleSheet(default_style)
                        logo_label.setText("🐾")

                else:
                    if logo_path:  # Only log if path is not empty
                        print(f"Logo file not found or not a file: {logo_path}")
                    # Ensure default state
                    logo_label.setStyleSheet(default_style)
                    logo_label.setText("🐾")
            else:
                # No valid logo path, use default
                logo_label.setStyleSheet(default_style)
                logo_label.setText("🐾")

        except Exception as e:
            print(f"Logo processing error: {e}")
            # Ensure we always have the default state
            logo_label.setStyleSheet(default_style)
            logo_label.setText("🐾")

        layout.addWidget(logo_label)

        # Text content - let it expand to fill available space
        text_layout = QVBoxLayout()
        text_layout.setSpacing(2)

        # App name
        name_label = QLabel(self.app_info['name'])
        name_label.setStyleSheet("font-size: 16px; font-weight: 600; color: #6B5B47;")
        text_layout.addWidget(name_label)

        # Description - allow it to expand and wrap
        desc_label = QLabel(self.app_info.get('description', ''))
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("font-size: 12px; color: #8B7D6B; line-height: 1.3;")
        # Allow description to expand vertically
        text_layout.addWidget(desc_label, 1)

        # Add text layout with stretch factor to fill available width
        layout.addLayout(text_layout, 1)

        self.setLayout(layout)

    def _load_logo_image(self, logo_path, logo_label, default_style):
        """Safely load logo image with comprehensive error handling."""
        try:
            # Check file extension
            valid_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg'}
            file_ext = os.path.splitext(logo_path)[1].lower()
            if file_ext not in valid_extensions:
                logo_label.setStyleSheet(default_style)
                logo_label.setText("🐾")
                return

            # Try to load the image with maximum safety
            try:
                # Create pixmap and load file
                pixmap = QPixmap()
                load_success = pixmap.load(logo_path)

                if load_success and not pixmap.isNull():
                    # Validate dimensions
                    width, height = pixmap.width(), pixmap.height()
                    if width > 0 and height > 0 and width <= 4096 and height <= 4096:
                        # Only now change the style and set the image
                        logo_label.setStyleSheet("border: none; border-radius: 8px; background-color: transparent;")
                        scaled_pixmap = pixmap.scaled(60, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        logo_label.setPixmap(scaled_pixmap)
                        logo_label.setText("")  # Clear text only after successful image load
                    else:
                        logo_label.setStyleSheet(default_style)
                        logo_label.setText("🐾")
                else:
                    logo_label.setStyleSheet(default_style)
                    logo_label.setText("🐾")

            except Exception:
                logo_label.setStyleSheet(default_style)
                logo_label.setText("🐾")

        except Exception:
            logo_label.setStyleSheet(default_style)
            logo_label.setText("🐾")

    def apply_warm_beige_style(self):
        style = """
        AppCard {
            background-color: #FEFCFA;
            border: 1px solid #E8DDD4;
            border-radius: 8px;
            margin: 2px;
        }
        AppCard:hover {
            background-color: #F8F5F2;
            border: 2px solid #D4C4B0;
        }
        """
        self.setStyleSheet(style)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.launch_app()
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPos())

    def show_context_menu(self, position):
        context_menu = QMenu(self)

        # Launch action
        launch_action = QAction("启动程序", self)
        launch_action.triggered.connect(self.launch_app)
        context_menu.addAction(launch_action)

        context_menu.addSeparator()

        # Edit action
        edit_action = QAction("修改配置", self)
        edit_action.triggered.connect(self.edit_app)
        context_menu.addAction(edit_action)

        context_menu.addSeparator()

        # Unregister action
        unregister_action = QAction("取消注册", self)
        unregister_action.triggered.connect(self.unregister_app)
        context_menu.addAction(unregister_action)

        context_menu.exec_(position)

    def launch_app(self):
        if self.parent_window:
            self.parent_window.launch_app(self.app_info['name'])

    def edit_app(self):
        if self.parent_window:
            self.parent_window.edit_app(self.app_info['name'])

    def unregister_app(self):
        if self.parent_window:
            self.parent_window.unregister_app(self.app_info['name'])


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.app_manager = AppManager()
        self.current_log_app = None
        self.log_timer = QTimer()
        self.log_timer.timeout.connect(self.refresh_logs)
        self.log_timer.start(500)  # Refresh logs every 500ms for more responsive updates

        self.setup_ui()
        self.apply_warm_beige_style()
        self.load_apps()

        # Register for log updates
        self.app_manager.add_log_callback(self.on_log_update)

    def setup_ui(self):
        self.setWindowTitle("the tumbled's Zoo")
        self.setMinimumSize(700, 600)
        self.resize(800, 650)

        # Set window icon
        current_file = os.path.abspath(__file__)
        unified_ui_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
        icon_path = os.path.join(unified_ui_dir, 'config', 'logo.png')

        if os.path.exists(icon_path):
            icon = QIcon(icon_path)
            if not icon.isNull():
                self.setWindowIcon(icon)

        # Create menu bar
        self.create_menu_bar()

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout()

        # Header
        header_layout = QHBoxLayout()

        # Title only (no logo)
        title_label = QLabel("the tumbled's zoo")
        title_label.setStyleSheet("font-size: 24px; font-weight: 600; color: #6B5B47;")

        # Register button
        self.register_btn = QPushButton("添加新动物")
        self.register_btn.clicked.connect(self.show_register_dialog)

        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.register_btn)

        layout.addLayout(header_layout)

        # Create splitter for apps and logs
        splitter = QSplitter(Qt.Vertical)

        # Apps vertical list
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.apps_widget = QWidget()
        self.apps_layout = QVBoxLayout()
        self.apps_layout.setSpacing(8)
        self.apps_layout.setContentsMargins(10, 10, 10, 10)
        self.apps_widget.setLayout(self.apps_layout)

        self.scroll_area.setWidget(self.apps_widget)

        # Add apps area to splitter
        splitter.addWidget(self.scroll_area)

        # Create log display area
        self.setup_log_area(splitter)

        # Set splitter proportions (70% apps, 30% logs)
        splitter.setSizes([500, 200])

        layout.addWidget(splitter)

        central_widget.setLayout(layout)

    def setup_log_area(self, splitter):
        """Setup the log display area."""
        log_group = QGroupBox("程序日志")
        log_layout = QVBoxLayout()

        # Log controls
        controls_layout = QHBoxLayout()

        # App selector
        app_label = QLabel("选择程序:")
        self.app_selector = QComboBox()
        self.app_selector.addItem("选择程序查看日志", "")
        self.app_selector.currentTextChanged.connect(self.on_app_selection_changed)

        # Open logs directory button
        self.open_logs_btn = QPushButton("打开日志目录")
        self.open_logs_btn.clicked.connect(self.open_logs_directory)
        self.open_logs_btn.setEnabled(True)

        controls_layout.addWidget(app_label)
        controls_layout.addWidget(self.app_selector, 1)
        controls_layout.addWidget(self.open_logs_btn)

        log_layout.addLayout(controls_layout)

        # Log display
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setMinimumHeight(100)  # Set minimum height instead of maximum
        self.log_display.setPlaceholderText("选择一个程序查看其运行日志...")

        log_layout.addWidget(self.log_display)
        log_group.setLayout(log_layout)

        splitter.addWidget(log_group)

    def update_app_selector(self):
        """Update the app selector with current apps."""
        current_selection = self.app_selector.currentData()
        self.app_selector.clear()
        self.app_selector.addItem("选择程序查看日志", "")

        apps = self.app_manager.get_registered_apps()
        for app in apps:
            app_name = app['name']
            # Check if app has logs
            log_path = self.app_manager.get_app_log_path(app_name)
            if os.path.exists(log_path):
                # Show running status
                if app_name in self.app_manager.get_running_processes():
                    display_name = f"● {app_name} (运行中)"
                else:
                    display_name = f"○ {app_name}"
                self.app_selector.addItem(display_name, app_name)

        # Restore selection if possible
        if current_selection:
            index = self.app_selector.findData(current_selection)
            if index >= 0:
                self.app_selector.setCurrentIndex(index)

    def on_app_selection_changed(self):
        """Handle app selection change."""
        app_name = self.app_selector.currentData()
        if app_name:
            self.current_log_app = app_name
            self.refresh_logs()
        else:
            self.current_log_app = None
            self.log_display.clear()
            self.log_display.setPlaceholderText("选择一个程序查看其运行日志...")

    def refresh_logs(self):
        """Refresh the log display."""
        try:
            # Check if UI components are still valid
            if not self.log_display or not hasattr(self, 'current_log_app'):
                return

            if self.current_log_app:
                logs = self.app_manager.get_app_logs(self.current_log_app, max_lines=50)

                # Additional safety check before accessing Qt objects
                if hasattr(self.log_display, 'toPlainText') and logs != self.log_display.toPlainText():
                    # Save scroll position
                    scrollbar = self.log_display.verticalScrollBar()
                    if scrollbar:
                        was_at_bottom = scrollbar.value() == scrollbar.maximum()
                    else:
                        was_at_bottom = True

                    self.log_display.setPlainText(logs)

                    # Auto-scroll to bottom if we were already at bottom
                    if was_at_bottom and scrollbar:
                        scrollbar.setValue(scrollbar.maximum())
        except Exception as e:
            # Silently handle any Qt-related errors during shutdown
            print(f"Error refreshing logs: {e}")

    def open_logs_directory(self):
        """Open the logs directory in Finder."""
        import subprocess
        logs_dir = self.app_manager.logs_dir
        try:
            subprocess.run(['open', logs_dir], check=True)
        except Exception as e:
            msg = QMessageBox(self)
            msg.setWindowTitle("错误")
            msg.setText(f"无法打开日志目录: {str(e)}")
            msg.setIcon(QMessageBox.NoIcon)
            msg.setStandardButtons(QMessageBox.Ok)
            msg.setFixedSize(320, 140)
            msg.exec_()

    def on_log_update(self, app_name: str, log_content: str):
        """Handle real-time log updates."""
        try:
            # Check if UI is still valid
            if not hasattr(self, 'app_selector') or not self.app_selector:
                return

            # Update app selector to show running status
            self.update_app_selector()

            # If this is the currently selected app, refresh display
            if app_name == self.current_log_app:
                self.refresh_logs()
        except Exception as e:
            # Silently handle any Qt-related errors during shutdown
            print(f"Error updating logs: {e}")

    def closeEvent(self, event):
        """Handle window close event."""
        try:
            # Stop the log timer
            if hasattr(self, 'log_timer') and self.log_timer:
                self.log_timer.stop()

            # Remove log callback to prevent crashes during shutdown
            if hasattr(self, 'app_manager') and self.app_manager:
                self.app_manager.remove_log_callback(self.on_log_update)
        except Exception as e:
            print(f"Error during cleanup: {e}")

        # Accept the close event
        event.accept()

    def create_menu_bar(self):
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('文件')

        register_action = QAction('添加新动物', self)
        register_action.triggered.connect(self.show_register_dialog)
        file_menu.addAction(register_action)

        file_menu.addSeparator()

        refresh_action = QAction('刷新', self)
        refresh_action.triggered.connect(self.load_apps)
        file_menu.addAction(refresh_action)

        file_menu.addSeparator()

        exit_action = QAction('退出', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Help menu
        help_menu = menubar.addMenu('帮助')

        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def apply_warm_beige_style(self):
        style = """
        QMainWindow {
            background-color: #FEFCFA;
        }
        QMenuBar {
            background-color: #FEFCFA;
            color: #6B5B47;
            border-bottom: 1px solid #E8DDD4;
            padding: 4px;
        }
        QMenuBar::item {
            background-color: transparent;
            padding: 6px 12px;
            border-radius: 4px;
        }
        QMenuBar::item:selected {
            background-color: #F0E6D6;
        }
        QMenu {
            background-color: #FEFCFA;
            color: #6B5B47;
            border: 1px solid #E8DDD4;
            border-radius: 6px;
        }
        QMenu::item {
            padding: 8px 16px;
        }
        QMenu::item:selected {
            background-color: #F0E6D6;
        }
        QPushButton {
            background-color: #F0E6D6;
            border: 1px solid #D4C4B0;
            border-radius: 8px;
            padding: 10px 20px;
            color: #6B5B47;
            font-weight: 500;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #E8DDD4;
        }
        QPushButton:pressed {
            background-color: #DDD2C6;
        }
        QScrollArea {
            border: none;
            background-color: transparent;
        }
        QScrollBar:vertical {
            background-color: #F5F2EE;
            width: 8px;
            border-radius: 4px;
            margin: 0px;
        }
        QScrollBar::handle:vertical {
            background-color: #D4C4B0;
            border-radius: 4px;
            min-height: 30px;
            margin: 2px;
        }
        QScrollBar::handle:vertical:hover {
            background-color: #C4B49F;
        }
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }
        QGroupBox {
            font-weight: 600;
            color: #6B5B47;
            border: 1px solid #E8DDD4;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            background-color: #FEFCFA;
        }
        QComboBox {
            background-color: #FEFCFA;
            border: 1px solid #E8DDD4;
            border-radius: 6px;
            padding: 6px 12px;
            color: #6B5B47;
            font-size: 13px;
        }
        QComboBox:hover {
            border-color: #D4C4B0;
        }
        QComboBox::drop-down {
            border: none;
        }
        QComboBox::down-arrow {
            image: none;
            border: none;
        }
        QComboBox QAbstractItemView {
            background-color: #FEFCFA;
            border: 1px solid #E8DDD4;
            border-radius: 6px;
            color: #6B5B47;
            selection-background-color: #F0E6D6;
        }
        QTextEdit {
            background-color: #FEFCFA;
            border: 1px solid #E8DDD4;
            border-radius: 6px;
            padding: 8px;
            color: #6B5B47;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        QSplitter::handle {
            background-color: #E8DDD4;
            height: 2px;
        }
        QSplitter::handle:hover {
            background-color: #D4C4B0;
        }
        """
        self.setStyleSheet(style)

    def load_apps(self):
        try:
            # Clear existing apps more safely
            # Remove all items from layout (widgets and spacers)
            while self.apps_layout.count():
                item = self.apps_layout.takeAt(0)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.hide()
                        widget.deleteLater()

            # Load registered apps
            apps = self.app_manager.get_registered_apps()

            if not apps:
                # Show empty state
                empty_label = QLabel("动物园还没有小动物\n点击右上角'添加新动物'按钮来添加吧")
                empty_label.setAlignment(Qt.AlignCenter)
                empty_label.setStyleSheet("color: #A0937D; font-size: 16px; padding: 50px;")
                self.apps_layout.addWidget(empty_label)
            else:
                # Add app cards vertically
                for app_info in apps:
                    try:
                        app_card = AppCard(app_info, self)
                        self.apps_layout.addWidget(app_card)
                    except Exception as e:
                        print(f"Error creating AppCard for {app_info.get('name', 'Unknown')}: {e}")

                # Add stretch to push cards to top
                self.apps_layout.addStretch()

            # Update app selector
            self.update_app_selector()

        except Exception as e:
            print(f"Exception in load_apps: {e}")

    def safe_refresh_apps(self):
        """Safely refresh apps with a delay to avoid memory issues."""
        try:
            # Use QTimer to defer the refresh to avoid immediate memory issues
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(50, self.load_apps)

        except Exception as e:
            print(f"Exception in safe_refresh_apps: {e}")

    def show_register_dialog(self):
        dialog = RegisterDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_apps()

    def launch_app(self, app_name):
        try:
            if self.app_manager.launch_app(app_name):
                # 程序启动成功，不显示弹窗
                pass
            else:
                msg = QMessageBox(self)
                msg.setWindowTitle("错误")
                msg.setText(f"程序 '{app_name}' 启动失败！")
                msg.setIcon(QMessageBox.NoIcon)
                msg.setStandardButtons(QMessageBox.Ok)
                msg.setFixedSize(320, 140)
                msg.exec_()
        except Exception as e:
            msg = QMessageBox(self)
            msg.setWindowTitle("错误")
            msg.setText(f"启动程序时发生错误: {str(e)}")
            msg.setIcon(QMessageBox.NoIcon)
            msg.setStandardButtons(QMessageBox.Ok)
            msg.setFixedSize(350, 160)
            msg.exec_()

    def edit_app(self, app_name):
        try:
            # Get current app info
            apps = self.app_manager.get_registered_apps()

            app_info = None
            for app in apps:
                if app['name'] == app_name:
                    app_info = app
                    break

            if not app_info:
                msg = QMessageBox(self)
                msg.setWindowTitle("错误")
                msg.setText(f"找不到程序 '{app_name}'")
                msg.setIcon(QMessageBox.NoIcon)
                msg.setStandardButtons(QMessageBox.Ok)
                msg.setFixedSize(320, 140)
                msg.exec_()
                return

            # Create edit dialog with current info
            from .edit_dialog import EditDialog
            dialog = EditDialog(app_info, self)

            result = dialog.exec_()

            if result == QDialog.Accepted:
                # Use a safer refresh method that doesn't recreate all widgets
                self.safe_refresh_apps()
        except Exception as e:
            print(f"Exception in edit_app: {e}")

    def unregister_app(self, app_name):
        dialog = CustomConfirmDialog(
            "确认取消注册",
            f"确定要取消注册程序 '{app_name}' 吗？",
            self
        )

        if dialog.exec_() == QDialog.Accepted:
            try:
                self.app_manager.unregister_app(app_name)
                self.show_success_message(f"程序 '{app_name}' 已取消注册！")
                self.load_apps()  # Refresh the app list
            except Exception as e:
                msg = QMessageBox(self)
                msg.setWindowTitle("错误")
                msg.setText(f"取消注册失败: {str(e)}")
                msg.setIcon(QMessageBox.NoIcon)
                msg.setStandardButtons(QMessageBox.Ok)
                msg.setFixedSize(350, 160)
                msg.exec_()

    def show_success_message(self, message):
        """Show a properly styled success message without icon."""
        msg = QMessageBox(self)
        msg.setWindowTitle("成功")
        msg.setText(message)
        msg.setIcon(QMessageBox.NoIcon)  # Remove icon to prevent text clipping
        msg.setStandardButtons(QMessageBox.Ok)

        # Set more compact size
        msg.setFixedSize(280, 120)

        # Apply custom styling for better visibility
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #F5F2EE;
                color: #6B5B47;
                font-size: 14px;
                border-radius: 8px;
            }
            QMessageBox QLabel {
                color: #6B5B47;
                font-size: 14px;
                padding: 15px 20px;
                text-align: center;
                min-width: 200px;
            }
            QMessageBox QPushButton {
                background-color: #F0E6D6;
                border: 1px solid #D4C4B0;
                border-radius: 6px;
                padding: 8px 20px;
                color: #6B5B47;
                font-weight: 500;
                min-width: 60px;
                margin: 5px;
            }
            QMessageBox QPushButton:hover {
                background-color: #E8DDD4;
            }
        """)

        msg.exec_()

    def show_about(self):
        msg = QMessageBox(self)
        msg.setWindowTitle("关于")
        msg.setText("the tumbled's zoo v1.0\n\n"
                   "动物主题的程序启动器\n"
                   "采用暖米色莫兰迪配色方案")
        msg.setIcon(QMessageBox.NoIcon)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setFixedSize(350, 180)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #F5F2EE;
                color: #6B5B47;
                font-size: 14px;
                border-radius: 8px;
            }
            QMessageBox QLabel {
                color: #6B5B47;
                font-size: 14px;
                padding: 15px 20px;
                text-align: center;
                min-width: 200px;
            }
            QMessageBox QPushButton {
                background-color: #F0E6D6;
                border: 1px solid #D4C4B0;
                border-radius: 6px;
                padding: 8px 20px;
                color: #6B5B47;
                font-weight: 500;
                min-width: 60px;
                margin: 5px;
            }
            QMessageBox QPushButton:hover {
                background-color: #E8DDD4;
            }
        """)
        msg.exec_()


def main():
    app = QApplication(sys.argv)
    app.setApplicationName("the tumbled's zoo")
    app.setApplicationDisplayName("the tumbled's zoo")
    app.setApplicationVersion("1.0")

    # Set application icon
    current_file = os.path.abspath(__file__)
    unified_ui_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
    icon_path = os.path.join(unified_ui_dir, 'config', 'logo.png')

    if os.path.exists(icon_path):
        app_icon = QIcon(icon_path)
        if not app_icon.isNull():
            app.setWindowIcon(app_icon)

    window = MainWindow()
    window.show()

    sys.exit(app.exec_())
