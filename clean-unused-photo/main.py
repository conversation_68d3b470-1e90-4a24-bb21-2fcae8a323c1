import sys
import json
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                           QHBoxLayout, QLabel, QLineEdit, QPushButton,
                           QTextEdit, QFileDialog, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from clean_unused_photos import find_unused_images

CONFIG_FILE = 'config.json'

class CleanThread(QThread):
    output_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(list)

    def __init__(self, target_dir):
        super().__init__()
        self.target_dir = target_dir

    def run(self):
        self.output_signal.emit(f"开始扫描目录: {self.target_dir}")
        
        try:
            results = find_unused_images(self.target_dir)
            self.finished_signal.emit(results)
        except Exception as e:
            self.output_signal.emit(f"错误: {str(e)}")
            self.finished_signal.emit([])

class CleanerUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.unused_photos = []
        self.init_ui()
        self.load_config()

    def init_ui(self):
        self.setWindowTitle('清理未使用的图片')
        self.setGeometry(100, 100, 800, 600)

        # 创建主窗口部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 目标目录设置
        dir_layout = QHBoxLayout()
        dir_label = QLabel('目标目录:')
        self.dir_input = QLineEdit()
        browse_button = QPushButton('浏览')
        browse_button.clicked.connect(self.browse_directory)
        dir_layout.addWidget(dir_label)
        dir_layout.addWidget(self.dir_input)
        dir_layout.addWidget(browse_button)
        layout.addLayout(dir_layout)

        # 按钮区域
        button_layout = QHBoxLayout()
        scan_button = QPushButton('开始扫描')
        scan_button.clicked.connect(self.start_scan)
        delete_button = QPushButton('删除未使用图片')
        delete_button.clicked.connect(self.delete_unused_photos)
        button_layout.addWidget(scan_button)
        button_layout.addWidget(delete_button)
        layout.addLayout(button_layout)

        # 输出日志区域
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        layout.addWidget(self.output_text)

    def browse_directory(self):
        dir_name = QFileDialog.getExistingDirectory(
            self, '选择目标目录', self.dir_input.text() or os.path.expanduser('~')
        )
        if dir_name:
            self.dir_input.setText(dir_name)
            self.save_config()

    def load_config(self):
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r') as f:
                    config = json.load(f)
                    self.dir_input.setText(config.get('target_dir', ''))
        except Exception as e:
            QMessageBox.warning(self, '警告', f'加载配置文件失败: {str(e)}')

    def save_config(self):
        try:
            config = {
                'target_dir': self.dir_input.text()
            }
            with open(CONFIG_FILE, 'w') as f:
                json.dump(config, f)
        except Exception as e:
            QMessageBox.warning(self, '警告', f'保存配置文件失败: {str(e)}')

    def start_scan(self):
        target_dir = self.dir_input.text()
        if not target_dir or not os.path.exists(target_dir):
            QMessageBox.warning(self, '错误', '请选择有效的目标目录')
            return

        # 清空输出区域
        self.output_text.clear()
        self.unused_photos = []
        
        # 创建并启动扫描线程
        self.clean_thread = CleanThread(target_dir)
        self.clean_thread.output_signal.connect(self.update_output)
        self.clean_thread.finished_signal.connect(self.scan_finished)
        self.clean_thread.start()

    def update_output(self, text):
        self.output_text.append(text)

    def scan_finished(self, results):
        self.unused_photos = results
        if not results:
            self.output_text.append("\n扫描完成，未发现未使用的图片。")
        else:
            self.output_text.append(f"\n扫描完成，发现 {len(results)} 个未使用的图片：")
            for photo in results:
                self.output_text.append(f"- {photo}")

    def delete_unused_photos(self):
        if not self.unused_photos:
            QMessageBox.warning(self, '警告', '没有可删除的图片')
            return

        reply = QMessageBox.question(
            self, '确认删除',
            f'确定要删除所有未使用的图片吗？共 {len(self.unused_photos)} 个文件',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            deleted_count = 0
            for photo_path in self.unused_photos:
                try:
                    os.remove(photo_path)
                    self.output_text.append(f"已删除: {photo_path}")
                    deleted_count += 1
                except Exception as e:
                    self.output_text.append(f"删除失败 {photo_path}: {str(e)}")

            self.output_text.append(f"\n成功删除 {deleted_count} 个图片")
            self.unused_photos = []

def main():
    app = QApplication(sys.argv)
    window = CleanerUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()