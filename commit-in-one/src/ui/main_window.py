import sys
import os
from PyQt5.QtWidgets import (
    <PERSON>A<PERSON><PERSON>, QMainWindow, QTabWidget, QVBoxLayout, QWidget,
    QLabel, QMessageBox
)
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt

from src.config.config_manager import ConfigManager
from src.core.user_manager import UserManager
from src.core.repo_manager import RepoManager
from src.ui.repo_page import RepoPage
from src.ui.user_page import UserPage
from src.ui.config_page import ConfigPage
from src.ui.styles import AppStyles


class MainWindow(QMainWindow):
    """
    Main application window with tabs for repository and user management.
    """

    def __init__(self):
        super().__init__()

        # Initialize configuration and managers
        # Use absolute path to ensure config file is always found correctly
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, "..", "..", "config.json")
        config_path = os.path.abspath(config_path)  # Convert to absolute path

        print(f"Git Commit Tool: Using config file: {config_path}")
        self.config_manager = ConfigManager(config_path)
        self.user_manager = UserManager(self.config_manager)
        self.repo_manager = RepoManager(self.config_manager, self.user_manager)

        # Set up the UI
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Set window properties
        self.setWindowTitle("Git Repository Commit Tool")
        # Try multiple locations for the icon
        icon_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "config", "logo.png"),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources", "logo.png")
        ]

        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                print(f"Setting window icon from: {icon_path}")
                self.setWindowIcon(QIcon(icon_path))
                break
        else:
            print("Warning: Could not find window icon")
        self.setMinimumSize(1000, 700)  # Increased window size

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # Create header with title
        title_label = QLabel("Git Repository Commit Tool")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 22px; font-weight: 500; margin: 15px 0; color: #5E8CA0;")
        main_layout.addWidget(title_label)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create repository page
        self.repo_page = RepoPage(self.repo_manager, self.user_manager, self.config_manager)
        self.tab_widget.addTab(self.repo_page, "Repositories")

        # Create user page
        self.user_page = UserPage(self.user_manager, self.repo_manager)
        self.tab_widget.addTab(self.user_page, "Users")

        # Create configuration page
        self.config_page = ConfigPage(self.config_manager)
        self.tab_widget.addTab(self.config_page, "Configuration")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

        # Connect signals
        self.user_page.user_updated.connect(self.repo_page.refresh_repositories)
        self.config_page.config_changed.connect(self.on_config_changed)

        # Show the window
        self.show()

    def on_config_changed(self):
        """Handle configuration changes."""
        # Update repo page with new concurrent settings
        concurrent_ops = self.config_manager.get_setting('concurrent_operations', 5)
        print(f"Configuration changed: concurrent_operations = {concurrent_ops}")

        # Notify repo page about config change
        if hasattr(self.repo_page, 'update_concurrent_settings'):
            self.repo_page.update_concurrent_settings(concurrent_ops)

    def closeEvent(self, event):
        """Handle window close event."""
        try:
            print("Git Commit Tool: Closing application...")

            # Save configuration before closing
            self.config_manager.save()

            # Clean up any running operations
            if hasattr(self.repo_page, 'cleanup'):
                self.repo_page.cleanup()

            print("Git Commit Tool: Cleanup completed")
            event.accept()
        except Exception as e:
            print(f"Git Commit Tool: Error during cleanup: {e}")
            event.accept()  # Accept anyway to prevent hanging


def main():
    """Main application entry point."""
    import signal

    # Handle signals gracefully
    def signal_handler(signum, frame):
        print(f"Git Commit Tool: Received signal {signum}, exiting gracefully...")
        QApplication.quit()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    app = QApplication(sys.argv)

    # Set application properties
    app.setQuitOnLastWindowClosed(True)

    # Set application icon
    # Try multiple locations for the icon
    icon_paths = [
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "config", "logo.png"),
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources", "logo.png")
    ]

    for icon_path in icon_paths:
        if os.path.exists(icon_path):
            print(f"Setting application icon from: {icon_path}")
            app_icon = QIcon(icon_path)
            app.setWindowIcon(app_icon)
            break
    else:
        print("Warning: Could not find application icon")

    # Apply application style
    AppStyles.apply_application_style(app)

    # Create and show the main window
    try:
        window = MainWindow()

        # Start the application event loop
        result = app.exec_()
        print(f"Git Commit Tool: Application exited with code {result}")
        sys.exit(result)
    except Exception as e:
        print(f"Git Commit Tool: Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
